import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:saymee_flutter/core/constants/app_data.dart';
import 'package:saymee_flutter/core/network/dio_exceptions.dart';
import 'package:saymee_flutter/core/network/dio_failure.dart';
import 'package:saymee_flutter/core/utils/utils.dart';
import 'package:saymee_flutter/modules/auth/data/datasources/auth_api.dart';

class AuthRepository {
  // ---- params ----
  final AuthApi api;

  AuthRepository({required this.api});

  // ---- methods ----
  Future<Either<DioFailure, Map<String, dynamic>>> getLoginOtp({
    required String phone,
  }) async {
    try {
      final response = await api.getLoginOtp(phone: phone);
      Utils.debugLog('loginOtp response = $response');

      final statusCode = response.statusCode.toString();
      final Map<String, dynamic> mapData = response.data;
      final result = mapData['errors'];
      if (result == null) {
        return Right({'statusCode': statusCode});
      } else {
        final reason = mapData['errors'][0]['message'] ?? '';
        final code = statusCode;
        return Left(
          ApiFailure(reason: reason, statusCode: statusCode, code: code),
        );
      }
    } on DioException catch (e) {
      final reason = DioExceptions.fromDioError(e).toString();
      final statusCode = e.response?.statusCode.toString() ?? '';
      return Left(ApiFailure(reason: reason, statusCode: statusCode));
    } on Exception catch (e) {
      return Left(ApiFailure(reason: e.toString(), statusCode: '400'));
    }
  }
}
