// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get emailCannotEmpty => 'Email cannot be empty';

  @override
  String get invalidEmail => 'Invalid email';

  @override
  String get passwordCannotEmpty => 'Password cannot be empty!';

  @override
  String get passwordShorter6Character =>
      'Password cannot be shorter than 6 characters!';

  @override
  String get passwordError => 'Passwords do not match, please enter again!';

  @override
  String get phoneNumberCannotEmpty => 'Phone number cannot be empty';

  @override
  String get invalidPhoneNumber => 'Invalid phone number';

  @override
  String get pleaseTryAgain => 'An error occurred, please try again.';

  @override
  String get sessionHasExpired => 'Session has expired!';

  @override
  String get close => 'Close';

  @override
  String get notification => 'Notification';

  @override
  String get signing => 'Signing';

  @override
  String get signIn => 'Sign in';

  @override
  String get enterEmail => 'Enter email';

  @override
  String get password => 'Password';

  @override
  String get enterPassword => 'Enter password';

  @override
  String get forgotPassword => 'Forgot password';

  @override
  String get termsAndConditions => 'Terms and conditions';

  @override
  String get signOut => 'Sign out';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirmSignOut =>
      'Are you sure you want to log out of this account?';

  @override
  String get success => 'Success';

  @override
  String get fullNameCannotEmpty => 'Full name cannot be empty';

  @override
  String get confirmPassword => 'Confirm password';

  @override
  String get goodMorning => 'Good morning';

  @override
  String get goodAfternoon => 'Good afternoon';

  @override
  String get goodEvening => 'Good evening';

  @override
  String get goodNight => 'Good night';

  @override
  String get usernameCannotEmpty => 'Username is cannot empty!';

  @override
  String get usernameInvalid => 'Username invalid!';

  @override
  String get otpCannotEmpty => 'OTP cannot empty!';

  @override
  String get groupNameCannotEmpty => 'Group name cannot empty!';

  @override
  String confirmCreateRoom(String name) {
    return 'You already have a chat session with $name. Would you like to continue existing session or start a new one ?';
  }

  @override
  String get continueTitle => 'Continue';

  @override
  String get newSession => 'New Session';

  @override
  String get chatbotList => 'Chatbot List';

  @override
  String get onboardingContent1 => 'Group Chat with AI Chatbot';

  @override
  String get onboardingContent2 => 'Effective advice in relationships';

  @override
  String get onboardingContent3 => 'Ask ANYTHING with your friends';

  @override
  String get recent => 'Recent';

  @override
  String get home => 'Home';

  @override
  String get chat => 'Chat';

  @override
  String get room => 'Room';

  @override
  String get profile => 'Profile';

  @override
  String get skip => 'Skip';

  @override
  String get activateAccountSuccess => 'Activate account success';

  @override
  String get activating => 'Activating';

  @override
  String get activateAccount => 'Activate account';

  @override
  String get otpSentToEmail => 'An OTP has been sent to the email ';

  @override
  String get otpToActivateAccount =>
      '. Please use that OTP to activate your account.';

  @override
  String get otpCode => 'OTP Code';

  @override
  String get inputOtpCode => 'Input OTP Code';

  @override
  String get enterEmailForResetPassword =>
      'Enter your email address to receive the code and instructions to reset your password.';

  @override
  String get inputYourEmail => 'Input your email';

  @override
  String get signUp => 'Sign Up';

  @override
  String get signUpWithEmail => 'Sign up with Email';

  @override
  String get fullname => 'Full name';

  @override
  String get inputYourFullname => 'Input your full name';

  @override
  String get username => 'Username';

  @override
  String get chooseAnUsername => 'Choose an username';

  @override
  String get registering => 'Registering';

  @override
  String get resetPasswordSuccess => 'Reset password success';

  @override
  String get resetPassword => 'Reset password';

  @override
  String get otpToResetPassword => '. Please use that OTP to reset password.';

  @override
  String get signInWithEmail => 'Sign in with Email';

  @override
  String get signInWithApple => 'Sign in with Apple';

  @override
  String get notAccount => 'You don’t have an account?';

  @override
  String get signUpHere => ' Sign up here';

  @override
  String get byUsingService => 'By using our services you are agreeing to our ';

  @override
  String get terms => 'Terms';

  @override
  String get termAndConditions => 'Term & Conditions';

  @override
  String get and => ' and ';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get or => 'Or';

  @override
  String get yes => 'Yes';

  @override
  String get blockMember => 'Block a member';

  @override
  String get removeFromGroup => 'Remove from group';

  @override
  String get unblockMember => 'Unblock a member';

  @override
  String get selectChatbot => 'Select Chatbot';

  @override
  String get groupIntro => 'Group Introduction';

  @override
  String get save => 'Save';

  @override
  String get writeShortDescription => 'Write a short descriptions';

  @override
  String get search => 'Search';

  @override
  String get findGroupsToJoin => 'Find groups to join';

  @override
  String get yourPersonalFriend => 'Your Personal A.I Companion';

  @override
  String get yourPersonalFriendContent =>
      'Engages in natural and meaningful conversations with humor, empathy, and creativity.';

  @override
  String get startConversation => 'Start a conversation';

  @override
  String get chats => 'Chats';

  @override
  String get openSetting => 'Open setting';

  @override
  String get noPermission => 'No permission';

  @override
  String get pleaseOpenSetting =>
      'Please open settings to grant access to camera';

  @override
  String get howCanIHelpYou => 'Hello, How can i help you?';

  @override
  String get addMemberToCreateGroup => 'Add member to create group';

  @override
  String get clearContext => 'Clear context';

  @override
  String get clearMessage => 'Clear Message';

  @override
  String get clearMessageContent =>
      'Clearing message data is permanent, the messages cannot be recovered.';

  @override
  String get delete => 'Delete';

  @override
  String get confirmDelete => 'Are you sure you want to delete?';

  @override
  String get members => 'members';

  @override
  String get talkChatbot => 'Talk Chatbot';

  @override
  String get creatingRoom => 'Creating room';

  @override
  String get createGroup => 'Create Group';

  @override
  String get done => 'Done';

  @override
  String get nameGroupChat => 'Name group chat';

  @override
  String get inputNameGroupChat => 'Input name group chat';

  @override
  String get uploadImage => 'Upload image';

  @override
  String get groupMembers => 'Group Members';

  @override
  String get updateSuccess => 'Update success!';

  @override
  String get clearSuccess => 'Clear success!';

  @override
  String get clear => 'Clear';

  @override
  String get mute => 'Mute';

  @override
  String get unmute => 'Unmute';

  @override
  String get leave => 'Leave';

  @override
  String get leaveGroup => 'Leave Group';

  @override
  String get leaveGroupContent => 'Are you sure you want to leave the group?';

  @override
  String get groupInfo => 'Group info';

  @override
  String get inviteLink => 'Invite Link';

  @override
  String get copyLink => 'Copy Link';

  @override
  String get copied => 'Copied';

  @override
  String get writeDescription =>
      'Write a short description and add photo of the group chat to introduce it to new users.';

  @override
  String get nameOrAvatarGroup => 'Name or avatar group';

  @override
  String get updateNameOrAvatarGroup =>
      'Updating the avatar and name makes the group easier to find and recognize.';

  @override
  String get setGroupPrivate => 'Set group as private';

  @override
  String get setGroupPrivateContent =>
      'The content of private group chats is not searchable within public channels, ensuring privacy for those conversations.';

  @override
  String get memberRequest => 'Member requests';

  @override
  String get memberRequestContent =>
      'Admins can approve requests to join group';

  @override
  String get groupManage => 'Group manage';

  @override
  String get deleteGroup => 'Delete group';

  @override
  String get deleteGroupContent =>
      'Deleting a group is permanent, all content will be irretrievably lost.';

  @override
  String get confirmDeleteGroup => 'Are you sure you want to delete group?';

  @override
  String get groupSetting => 'Group Settings';

  @override
  String get requesting => 'Requesting';

  @override
  String get joining => 'Joining';

  @override
  String get rejecting => 'Rejecting';

  @override
  String get deny => 'Deny';

  @override
  String get accept => 'Accept';

  @override
  String get requested => 'Requested';

  @override
  String get joinTheGroup => 'Join the group';

  @override
  String get groupInvitation => 'Group invitation';

  @override
  String get imageDownloadedSuccess => 'Image downloaded successfully!';

  @override
  String get share => 'Share';

  @override
  String get roomIntro => 'Experience the ultimate Group Chat with A.I';

  @override
  String get roomIntroContent =>
      'Invite your friends to join the chat and chat with the AI as a group.';

  @override
  String get createNewGroup => 'Create a new group';

  @override
  String get chatRoom => 'Chat Room';

  @override
  String get updating => 'Updating';

  @override
  String get credits => 'credits';

  @override
  String get forTitle => 'for';

  @override
  String get months => 'months';

  @override
  String get paymentSuccess => 'Payment success';

  @override
  String get paymentFailed => 'Payment failed';

  @override
  String get upgradeTo => 'Upgrade to ';

  @override
  String get premium => 'premium';

  @override
  String get plan => ' plan';

  @override
  String get andEnjoyAccess => 'And enjoy access to all features of MindMate ';

  @override
  String get noAds => 'No ads';

  @override
  String get accessModel => 'Access to the smartest AI model';

  @override
  String get fasterResponse => 'Faster response times';

  @override
  String get restorePurchases => 'Restore Purchases';

  @override
  String get subContent1 =>
      'Subscribed user has unlimited access to the services.';

  @override
  String get subContent2 =>
      'Payment will be charged to iTunes Account at purchase confirmation';

  @override
  String get subContent3 =>
      'Subscription automatically renews within 24-hours prior to the end of the current subscription period';

  @override
  String get subContent4 =>
      'Subscription may be managed and auto-renewal may be turned off by going to the Users Account Settings after purchased';

  @override
  String get subContent5 =>
      'Any unused portion of a free trial period, if offered,will be forfeited when user purchases a subscription to that publication, where applicable';

  @override
  String get expiryDate => 'Expiry date';

  @override
  String get expired => 'Expired';

  @override
  String get creditsTitle => 'Credits';

  @override
  String get upgradeToPremium => 'Upgrade to premium';

  @override
  String get subscribeNow => 'Subscribe Now';

  @override
  String get accountInfo => 'Account Information';

  @override
  String get edit => 'Edit';

  @override
  String get setNewPhoto => 'Set New Photo';

  @override
  String get logout => 'Log Out';

  @override
  String get confirmLogout => 'Are you sure you want to log out?';

  @override
  String get appConfiguration => 'App configuration';

  @override
  String get store => 'Store';

  @override
  String get enhanceYourExperience => 'Enhance your experience';

  @override
  String get rateApp => 'Rate app';

  @override
  String get shareWithFriend => 'Share with friend';

  @override
  String get helpAndSupport => 'Help and support';

  @override
  String get faq => 'FAQs';

  @override
  String get contactUs => 'Contact us';

  @override
  String get copyRight => 'Copyright ©2024 by MDC';

  @override
  String get later => 'Later';

  @override
  String get upgrade => 'Upgrade';

  @override
  String get hi_saymee => 'Hello Saymee here!!';

  @override
  String get placeholder_phone => 'Enter your phone number';

  @override
  String get label_phone => 'Phone';

  @override
  String get login_otp => 'Login with OTP';

  @override
  String get try_saymee => 'Try Saymee';

  @override
  String get confirm_otp => 'Confirm OTP';

  @override
  String message_otp(String phone) {
    return 'Confirmationc code has been sent to $phone. Please enter';
  }

  @override
  String get confirm => 'Confirm';

  @override
  String otp_expire(int seconds) {
    return 'OTP expire in $seconds seconds';
  }

  @override
  String get resend_otp => 'Resend OTP';

  @override
  String get invalid_otp => 'Invalid OTP';
}
